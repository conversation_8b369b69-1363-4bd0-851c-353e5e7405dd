import {
  fetchAuthSession as amplifyFetchAuthSession,
  signIn,
  signOut,
} from '@aws-amplify/auth'
import {jwtDecode} from 'jwt-decode'
import {createPinia, setActivePinia} from 'pinia'
import {afterEach, beforeEach, describe, expect, it, vi} from 'vitest'
import {useCognitoAuthStore} from '../cognitoAuth.js'

// Mock AWS Amplify Auth
vi.mock('@aws-amplify/auth', () => ({
  signIn: vi.fn(),
  signOut: vi.fn(),
  fetchAuthSession: vi.fn(),
}))

// Mock jwt-decode
vi.mock('jwt-decode', () => ({
  jwtDecode: vi.fn(),
}))

// Mock Vue Router
const mockRouter = {
  push: vi.fn(),
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter,
}))

// Mock useApi composable
const mockApiExecute = vi.fn()
vi.mock('../composables/useApi', () => ({
  default: () => ({
    apiExecute: mockApiExecute,
  }),
}))

// Mock vue3-cookies
vi.mock('vue3-cookies', () => ({
  useCookies: () => ({
    cookies: {
      get: vi.fn().mockReturnValue('mock-session-token'),
      remove: vi.fn(),
    },
  }),
}))

// Mock environment variables
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_API_ENDPOINT: 'https://test-api.example.com/api/',
    DEV: true,
  },
  writable: true,
})

describe('cognitoAuth Store', () => {
  let pinia
  let store

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    store = useCognitoAuthStore()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      expect(store.user).toBeNull()
      expect(store.idToken).toBeNull()
      expect(store.accessToken).toBeNull()
      expect(store.isAuthenticated).toBe(false)
    })
  })

  describe('fetchAuthSession', () => {
    it('should successfully fetch and set user session', async () => {
      const mockToken = 'mock-id-token'
      const mockAccessToken = 'mock-access-token'
      const mockDecodedToken = {
        email: '<EMAIL>',
        'custom:member_no': '123',
        'custom:user_no': '456',
        'custom:member_name': 'Test User',
        'custom:language_code': 'ja',
        'tenant-id': '1',
      }

      amplifyFetchAuthSession.mockResolvedValue({
        tokens: {
          idToken: {toString: () => mockToken},
          accessToken: {toString: () => mockAccessToken},
        },
      })
      jwtDecode.mockReturnValue(mockDecodedToken)

      const result = await store.fetchAuthSession()

      expect(result).toBe(true)
      expect(store.user).toEqual({
        email: '<EMAIL>',
        memberNo: '123',
        userNo: '456',
        memberName: 'Test User',
        languageCode: 'ja',
        tenantId: '1',
      })
      expect(store.idToken).toBe(mockToken)
      expect(store.accessToken).toBe(mockAccessToken)
      expect(store.isAuthenticated).toBe(true)
    })

    it('should handle session fetch failure', async () => {
      amplifyFetchAuthSession.mockRejectedValue(new Error('Session failed'))

      const result = await store.fetchAuthSession()

      expect(result).toBe(false)
      expect(store.user).toBeNull()
      expect(store.idToken).toBeNull()
      expect(store.accessToken).toBeNull()
      expect(store.isAuthenticated).toBe(false)
    })
  })

  describe('login', () => {
    it('should successfully login and log history', async () => {
      const mockSignInResponse = {
        isSignedIn: true,
      }
      const mockToken = 'mock-id-token'
      const mockDecodedToken = {
        email: '<EMAIL>',
        'custom:member_no': '123',
        'custom:user_no': '456',
        'custom:member_name': 'Test User',
        'custom:language_code': 'ja',
        'tenant-id': '1',
      }

      signIn.mockResolvedValue(mockSignInResponse)
      amplifyFetchAuthSession.mockResolvedValue({
        tokens: {
          idToken: {toString: () => mockToken},
          accessToken: {toString: () => 'mock-access-token'},
        },
      })
      jwtDecode.mockReturnValue(mockDecodedToken)
      mockApiExecute.mockResolvedValue({success: true})

      const result = await store.login('<EMAIL>', 'password123')

      expect(signIn).toHaveBeenCalledWith({
        username: '<EMAIL>',
        password: 'password123',
        options: {
          authFlowType: 'USER_PASSWORD_AUTH',
        },
      })
      expect(result).toEqual({type: 'SUCCESS'})
      expect(store.isAuthenticated).toBe(true)

      // Verify login history logging was called
      expect(mockApiExecute).toHaveBeenCalledWith(
        'private/login-history-logging',
        {
          languageCode: 'ja',
        },
        false, // not a file upload
        {
          authorization: `Bearer ${mockToken}`,
        }
      )
    })

    it('should handle new password required', async () => {
      const mockSignInResponse = {
        isSignedIn: false,
        nextStep: {
          signInStep: 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED',
        },
      }

      signIn.mockResolvedValue(mockSignInResponse)

      const result = await store.login('<EMAIL>', 'password123')

      expect(result).toEqual({
        type: 'NEW_PASSWORD_REQUIRED',
        message: '初回ログイン時はパスワードの変更が必要です。',
      })
    })

    it('should handle disabled user error', async () => {
      const error = new Error('User is disabled.')
      error.name = 'NotAuthorizedException'
      signIn.mockRejectedValue(error)

      await expect(
        store.login('<EMAIL>', 'password123')
      ).rejects.toThrow(
        'このアカウントは無効化されています。管理者にお問い合わせください。'
      )
    })

    it('should handle authentication failure', async () => {
      const error = new Error('Incorrect username or password.')
      error.name = 'NotAuthorizedException'
      signIn.mockRejectedValue(error)

      await expect(
        store.login('<EMAIL>', 'wrongpassword')
      ).rejects.toThrow('ログインIDまたはパスワードが正しくありません。')
    })

    it('should continue login even if history logging fails', async () => {
      const mockSignInResponse = {
        isSignedIn: true,
      }
      const mockToken = 'mock-id-token'
      const mockDecodedToken = {
        email: '<EMAIL>',
        'custom:member_no': '123',
        'custom:user_no': '456',
        'custom:member_name': 'Test User',
        'custom:language_code': 'ja',
        'tenant-id': '1',
      }

      signIn.mockResolvedValue(mockSignInResponse)
      amplifyFetchAuthSession.mockResolvedValue({
        tokens: {
          idToken: {toString: () => mockToken},
          accessToken: {toString: () => 'mock-access-token'},
        },
      })
      jwtDecode.mockReturnValue(mockDecodedToken)
      mockApiExecute.mockRejectedValue(new Error('Network error'))

      const result = await store.login('<EMAIL>', 'password123')

      expect(result).toEqual({type: 'SUCCESS'})
      expect(store.isAuthenticated).toBe(true)
    })
  })

  describe('logout', () => {
    it('should successfully logout and clear state', async () => {
      // Set up authenticated state first
      store.user = {email: '<EMAIL>'}
      store.idToken = 'mock-token'
      store.accessToken = 'mock-access-token'

      signOut.mockResolvedValue()

      await store.logout()

      expect(signOut).toHaveBeenCalled()
      expect(store.user).toBeNull()
      expect(store.idToken).toBeNull()
      expect(store.accessToken).toBeNull()
      expect(mockRouter.push).toHaveBeenCalledWith('/login')
    })

    it('should handle logout error gracefully', async () => {
      store.user = {email: '<EMAIL>'}
      store.idToken = 'mock-token'
      store.accessToken = 'mock-access-token'

      signOut.mockRejectedValue(new Error('Logout failed'))

      await store.logout()

      // Should still clear state even if signOut fails
      expect(store.user).toBeNull()
      expect(store.idToken).toBeNull()
      expect(store.accessToken).toBeNull()
    })
  })

  describe('Error Handling', () => {
    it('should handle UserNotFoundException', async () => {
      const error = new Error('User does not exist.')
      error.name = 'UserNotFoundException'
      signIn.mockRejectedValue(error)

      await expect(
        store.login('<EMAIL>', 'password123')
      ).rejects.toThrow('ログインIDまたはパスワードが正しくありません。')
    })

    it('should handle LimitExceededException', async () => {
      const error = new Error('Attempt limit exceeded.')
      error.name = 'LimitExceededException'
      signIn.mockRejectedValue(error)

      await expect(
        store.login('<EMAIL>', 'password123')
      ).rejects.toThrow(
        'ログイン試行回数が制限を超えました。しばらくしてから再度お試しください。'
      )
    })

    it('should handle unknown errors', async () => {
      const error = new Error('Unknown error')
      error.name = 'UnknownError'
      signIn.mockRejectedValue(error)

      await expect(
        store.login('<EMAIL>', 'password123')
      ).rejects.toThrow('Unknown error')
    })
  })
})
