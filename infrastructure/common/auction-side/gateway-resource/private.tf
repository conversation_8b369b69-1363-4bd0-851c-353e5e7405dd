locals {
  private-resource = {
    path_name = "private"
  }
}

resource "aws_api_gateway_resource" "private-resource" {
  rest_api_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id   = var.root_resource_id
  path_part   = local.private-resource.path_name
}

module "bid-items" {
  source                                         = "./bid-items"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "favorite-item" {
  source                                         = "./favorite-item"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "change-language" {
  source                                         = "./change-language"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "favorite-stock" {
  source                                         = "./favorite-stock"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "print-auction-items-csv" {
  source                                         = "./print-auction-items-csv"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "login-history-logging" {
  source                                         = "./login-history-logging"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  cognito_user_pool_id                           = var.cognito_user_pool_id
  cognito_client_id                              = var.cognito_client_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "print-stock-items-csv" {
  source                                         = "./print-stock-items-csv"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-mypage-constants" {
  source                                         = "./get-mypage-constants"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-successful-bid-history" {
  source                                         = "./get-successful-bid-history"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-change-info-constants" {
  source                                         = "./get-change-info-constants"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "change-member-info" {
  source                                         = "./change-member-info"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "withdraw-member" {
  source                                         = "./withdraw-member"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "reload-item-status" {
  source                                         = "./reload-item-status"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "change-member-password" {
  source                                         = "./change-member-password"
  project_name                                   = var.project_name
  environment                                    = var.environment
  allow_origin                                   = var.allow_origin
  s3-bucket-arn                                  = var.s3-bucket-arn
  s3-bucket-id                                   = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id            = var.aws_api_gateway_rest_api_gateway_id
  parent_id                                      = aws_api_gateway_resource.private-resource.id
  parent_path                                    = "${local.private-resource.path_name}/"
  authorization                                  = "COGNITO_USER_POOLS"
  aws_api_gateway_authorizer_id                  = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name                           = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids                              = var.lambda_subnet_ids
  lambda_security_group_id                       = var.lambda_security_group_id
  lambda_global_environment_variables            = var.lambda_global_environment_variables
  slack-notification-lambda-arn                  = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}
